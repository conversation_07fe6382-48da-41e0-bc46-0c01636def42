import 'dart:io';
import 'package:flutter/foundation.dart';

/// Custom exception classes for better error handling
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => message;
}

class NetworkException extends AppException {
  NetworkException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class AuthenticationException extends AppException {
  AuthenticationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class ValidationException extends AppException {
  ValidationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

/// Error handler utility class
class ErrorHandler {
  /// Convert various error types to user-friendly messages
  static String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    }
    
    if (error is SocketException) {
      return 'No internet connection. Please check your network and try again.';
    }
    
    if (error is HttpException) {
      return 'Server error occurred. Please try again later.';
    }
    
    if (error is FormatException) {
      return 'Invalid data format received. Please try again.';
    }
    
    // Log the original error for debugging
    if (kDebugMode) {
      debugPrint('Unhandled error: $error');
    }
    
    return 'An unexpected error occurred. Please try again.';
  }

  /// Handle API response errors
  static AppException handleApiError(int statusCode, String? message) {
    switch (statusCode) {
      case 400:
        return ValidationException(message ?? 'Invalid request data');
      case 401:
        return AuthenticationException(message ?? 'Authentication failed');
      case 403:
        return AuthenticationException(message ?? 'Access denied');
      case 404:
        return NetworkException(message ?? 'Resource not found');
      case 500:
        return NetworkException(message ?? 'Server error occurred');
      case 503:
        return NetworkException(message ?? 'Service temporarily unavailable');
      default:
        return NetworkException(message ?? 'Network error occurred');
    }
  }

  /// Log errors for debugging and analytics
  static void logError(dynamic error, {StackTrace? stackTrace, String? context}) {
    if (kDebugMode) {
      debugPrint('Error in $context: $error');
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
    
    // In production, you might want to send errors to a crash reporting service
    // like Firebase Crashlytics, Sentry, etc.
  }
}

/// Loading state enum for better type safety
enum LoadingState {
  idle,
  loading,
  success,
  error,
}

/// Result wrapper for operations that can fail
class Result<T> {
  final T? data;
  final AppException? error;
  final bool isSuccess;

  Result.success(this.data) : error = null, isSuccess = true;
  Result.failure(this.error) : data = null, isSuccess = false;

  bool get isFailure => !isSuccess;
}
