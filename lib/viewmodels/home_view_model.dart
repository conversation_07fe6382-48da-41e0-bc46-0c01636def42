import '../models/auth_models.dart';
import '../repositories/auth_repository.dart';
import 'base_view_model.dart';

/// ViewModel for managing home screen state and business logic
class HomeViewModel extends BaseViewModel {
  final IAuthRepository _authRepository;
  User? _currentUser;
  String _welcomeMessage = '';
  List<String> _dashboardItems = [];

  HomeViewModel({IAuthRepository? authRepository})
      : _authRepository = authRepository ?? AuthRepository();

  /// Current user information
  User? get currentUser => _currentUser;

  /// Welcome message for the user
  String get welcomeMessage => _welcomeMessage;

  /// Dashboard items to display
  List<String> get dashboardItems => List.unmodifiable(_dashboardItems);

  /// Initialize the home screen data
  Future<void> initialize() async {
    await executeWithLoading(() async {
      await _loadUserData();
      await _loadDashboardData();
    });
  }

  /// Load current user data
  Future<void> _loadUserData() async {
    try {
      _currentUser = await _authRepository.getUser();
      _generateWelcomeMessage();
    } catch (e) {
      setError('Failed to load user data: $e');
      rethrow;
    }
  }

  /// Generate personalized welcome message
  void _generateWelcomeMessage() {
    if (_currentUser != null) {
      final name = _currentUser!.name ?? _currentUser!.email.split('@').first;
      final timeOfDay = _getTimeOfDay();
      _welcomeMessage = 'Good $timeOfDay, $name!';
    } else {
      _welcomeMessage = 'Welcome!';
    }
  }

  /// Get time of day for greeting
  String _getTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'morning';
    } else if (hour < 17) {
      return 'afternoon';
    } else {
      return 'evening';
    }
  }

  /// Load dashboard data
  Future<void> _loadDashboardData() async {
    try {
      // Simulate loading dashboard items
      // In a real app, this would fetch from an API
      _dashboardItems = [
        'Recent Activity',
        'Quick Actions',
        'Statistics',
        'Notifications',
        'Settings',
      ];
    } catch (e) {
      setError('Failed to load dashboard data: $e');
      rethrow;
    }
  }

  /// Refresh all home screen data
  Future<void> refresh() async {
    await executeWithLoading(() async {
      await _loadUserData();
      await _loadDashboardData();
    });
  }

  /// Refresh user data only
  Future<void> refreshUserData() async {
    await executeWithErrorHandling(() async {
      await _loadUserData();
    });
  }

  /// Handle logout action
  Future<bool> logout() async {
    final result = await executeWithLoading(() async {
      final success = await _authRepository.logout();
      
      if (success) {
        _currentUser = null;
        _welcomeMessage = '';
        _dashboardItems.clear();
      }
      
      return success;
    });

    return result ?? false;
  }

  /// Simulate performing a dashboard action
  Future<void> performDashboardAction(String action) async {
    await executeWithLoading(() async {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real app, this would perform the actual action
      // For now, just show a success message by clearing any errors
      clearError();
    }, showLoading: false);
  }
}
