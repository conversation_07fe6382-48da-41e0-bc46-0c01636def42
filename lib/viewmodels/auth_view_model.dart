import '../models/auth_models.dart';
import '../repositories/auth_repository.dart';
import 'base_view_model.dart';

/// ViewModel for handling authentication-related operations
class AuthViewModel extends BaseViewModel {
  final IAuthRepository _authRepository;
  User? _currentUser;
  bool _isLoggedIn = false;

  AuthViewModel({IAuthRepository? authRepository})
      : _authRepository = authRepository ?? AuthRepository();

  /// Current authenticated user
  User? get currentUser => _currentUser;

  /// Whether user is currently logged in
  bool get isLoggedIn => _isLoggedIn;

  /// Initialize the ViewModel by checking current auth status
  Future<void> initialize() async {
    await executeWithLoading(() async {
      await _checkAuthStatus();
    });
  }

  /// Check current authentication status
  Future<void> _checkAuthStatus() async {
    try {
      final isLoggedIn = await _authRepository.isLoggedIn();

      if (isLoggedIn) {
        // Verify token is still valid
        final isValid = await _authRepository.verifyToken();
        if (isValid) {
          _currentUser = await _authRepository.getUser();
          _isLoggedIn = true;
        } else {
          _currentUser = null;
          _isLoggedIn = false;
        }
      } else {
        _currentUser = null;
        _isLoggedIn = false;
      }
    } catch (e) {
      _currentUser = null;
      _isLoggedIn = false;
      rethrow;
    }
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    final result = await executeWithLoading(() async {
      final loginRequest = LoginRequest(
        email: email.trim(),
        password: password,
      );

      final response = await _authRepository.login(loginRequest);

      if (response.success && response.token != null) {
        _currentUser = response.user;
        _isLoggedIn = true;
        return true;
      } else {
        setError(response.message ?? 'Login failed. Please try again.');
        return false;
      }
    });

    return result ?? false;
  }

  /// Logout current user
  Future<bool> logout() async {
    final result = await executeWithLoading(() async {
      final success = await _authRepository.logout();
      
      if (success) {
        _currentUser = null;
        _isLoggedIn = false;
      }
      
      return success;
    });

    return result ?? false;
  }

  /// Verify current token validity
  Future<bool> verifyToken() async {
    final result = await executeWithErrorHandling(() async {
      final isValid = await _authRepository.verifyToken();
      
      if (!isValid) {
        _currentUser = null;
        _isLoggedIn = false;
      }
      
      return isValid;
    });

    return result ?? false;
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    await executeWithErrorHandling(() async {
      if (_isLoggedIn) {
        _currentUser = await _authRepository.getUser();
      }
    });
  }
}
