import 'package:flutter/foundation.dart';

/// Base ViewModel class that provides common functionality for all ViewModels
/// Implements ChangeNotifier for state management with Provider
abstract class BaseViewModel extends ChangeNotifier {
  bool _isLoading = false;
  bool _isDisposed = false;
  String? _errorMessage;

  /// Current loading state
  bool get isLoading => _isLoading;

  /// Current error message, null if no error
  String? get errorMessage => _errorMessage;

  /// Whether there's currently an error
  bool get hasError => _errorMessage != null;

  /// Whether the ViewModel has been disposed
  bool get isDisposed => _isDisposed;

  /// Set loading state and notify listeners
  void setLoading(bool loading) {
    if (_isDisposed) return;
    
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message and notify listeners
  void setError(String? error) {
    if (_isDisposed) return;
    
    _errorMessage = error;
    notifyListeners();
  }

  /// Clear current error
  void clearError() {
    if (_isDisposed) return;
    
    _errorMessage = null;
    notifyListeners();
  }

  /// Execute an async operation with automatic loading and error handling
  Future<T?> executeWithLoading<T>(
    Future<T> Function() operation, {
    bool showLoading = true,
    bool clearErrorOnStart = true,
  }) async {
    if (_isDisposed) return null;

    try {
      if (clearErrorOnStart) {
        clearError();
      }
      
      if (showLoading) {
        setLoading(true);
      }

      final result = await operation();
      return result;
    } catch (e) {
      setError(e.toString());
      return null;
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }

  /// Execute an async operation without loading state management
  Future<T?> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    bool clearErrorOnStart = true,
  }) async {
    if (_isDisposed) return null;

    try {
      if (clearErrorOnStart) {
        clearError();
      }

      final result = await operation();
      return result;
    } catch (e) {
      setError(e.toString());
      return null;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  void notifyListeners() {
    if (!_isDisposed) {
      super.notifyListeners();
    }
  }
}
