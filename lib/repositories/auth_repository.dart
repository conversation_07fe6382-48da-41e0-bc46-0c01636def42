import '../models/auth_models.dart';
import '../services/auth_service.dart';

/// Repository interface for authentication operations
abstract class IAuthRepository {
  Future<LoginResponse> login(LoginRequest request);
  Future<bool> logout();
  Future<bool> isLoggedIn();
  Future<String?> getToken();
  Future<User?> getUser();
  Future<bool> verifyToken();
}

/// Implementation of authentication repository using AuthService
class AuthRepository implements IAuthRepository {
  @override
  Future<LoginResponse> login(LoginRequest request) async {
    return await AuthService.login(request);
  }

  @override
  Future<bool> logout() async {
    return await AuthService.logout();
  }

  @override
  Future<bool> isLoggedIn() async {
    return await AuthService.isLoggedIn();
  }

  @override
  Future<String?> getToken() async {
    return await AuthService.getToken();
  }

  @override
  Future<User?> getUser() async {
    return await AuthService.getUser();
  }

  @override
  Future<bool> verifyToken() async {
    return await AuthService.verifyToken();
  }
}

/// Mock implementation for testing
class MockAuthRepository implements IAuthRepository {
  bool _isLoggedIn = false;
  User? _currentUser;
  String? _token;

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock successful <NAME_EMAIL>
    if (request.email == '<EMAIL>' && request.password == 'password') {
      _isLoggedIn = true;
      _token = 'mock_token_123';
      _currentUser = User(
        id: '1',
        email: request.email,
        name: 'Demo User',
      );
      
      return LoginResponse(
        success: true,
        token: _token,
        user: _currentUser,
        message: 'Login successful',
      );
    } else {
      return LoginResponse(
        success: false,
        message: 'Invalid credentials',
      );
    }
  }

  @override
  Future<bool> logout() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _isLoggedIn = false;
    _token = null;
    _currentUser = null;
    return true;
  }

  @override
  Future<bool> isLoggedIn() async {
    return _isLoggedIn;
  }

  @override
  Future<String?> getToken() async {
    return _token;
  }

  @override
  Future<User?> getUser() async {
    return _currentUser;
  }

  @override
  Future<bool> verifyToken() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _isLoggedIn && _token != null;
  }
}
