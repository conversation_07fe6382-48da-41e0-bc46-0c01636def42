import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/environment.dart';
import '../models/auth_models.dart';

class ApiService {
  static const Duration _timeout = Duration(seconds: 30);
  
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Map<String, String> _headersWithAuth(String token) => {
    ..._headers,
    'Authorization': 'Bearer $token',
  };

  static Future<ApiResponse<T>> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) async {
    try {
      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse<T>(
          success: true,
          data: fromJson != null ? fromJson(data) : data as T?,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse<T>(
          success: false,
          message: data['message'] ?? 'Request failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        message: 'Failed to parse response: $e',
        statusCode: response.statusCode,
      );
    }
  }

  static Future<ApiResponse<T>> _makeRequest<T>(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    String? token,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse(EnvironmentConfig.getApiEndpoint(endpoint));
      final headers = token != null ? _headersWithAuth(token) : _headers;
      
      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: headers).timeout(_timeout);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: headers,
            body: body != null ? json.encode(body) : null,
          ).timeout(_timeout);
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: headers,
            body: body != null ? json.encode(body) : null,
          ).timeout(_timeout);
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: headers).timeout(_timeout);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      return _handleResponse<T>(response, fromJson);
    } on SocketException {
      return ApiResponse<T>(
        success: false,
        message: 'No internet connection',
      );
    } on HttpException {
      return ApiResponse<T>(
        success: false,
        message: 'HTTP error occurred',
      );
    } on FormatException {
      return ApiResponse<T>(
        success: false,
        message: 'Invalid response format',
      );
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        message: 'Request failed: $e',
      );
    }
  }

  // GET request
  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    String? token,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>('GET', endpoint, token: token, fromJson: fromJson);
  }

  // POST request
  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    String? token,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>('POST', endpoint, body: body, token: token, fromJson: fromJson);
  }

  // PUT request
  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    String? token,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>('PUT', endpoint, body: body, token: token, fromJson: fromJson);
  }

  // DELETE request
  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    String? token,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>('DELETE', endpoint, token: token, fromJson: fromJson);
  }
}
